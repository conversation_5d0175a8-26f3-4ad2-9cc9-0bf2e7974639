{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-08-26T09:01:55.462957Z", "start_time": "2025-08-26T09:01:54.538306Z"}}, "source": ["from ib_async import *\n", "util.startLoop()\n", "\n", "ib = IB()\n", "# ib.connect('47.242.117.184', 4012, clientId=47)\n", "# ib.connect('127.0.0.1', 4002, clientId=47)\n", "ib.connect('192.168.1.54', 4011, clientId=47)"], "outputs": [{"data": {"text/plain": ["<IB connected to 192.168.1.54:4011 clientId=47>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "execution_count": 1}, {"cell_type": "code", "metadata": {}, "source": ["import logging\n", "util.logToConsole(logging.DEBUG)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {}, "source": "## Historical data"}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2025-06-17T05:52:42.920567Z", "start_time": "2025-06-17T05:52:42.442245Z"}}, "source": "To get the earliest date of available bar data the \"head timestamp\" can be requested:"}, {"metadata": {"ExecuteTime": {"end_time": "2025-08-20T09:27:48.050807Z", "start_time": "2025-08-20T09:27:47.103419Z"}}, "cell_type": "code", "source": ["contract = Stock('AAPL', 'SMART', 'USD')\n", "ib.qualifyContracts(contract)\n", "contract"], "outputs": [{"data": {"text/plain": ["Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {}, "cell_type": "code", "source": ["contract = Stock('AAPL', 'OVERNIGHT', 'USD')\n", "ib.qualifyContracts(contract)\n", "contract"], "execution_count": 3, "outputs": [{"data": {"text/plain": ["Stock(conId=265598, symbol='AAPL', exchange='OVERNIGHT', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}]}, {"metadata": {"ExecuteTime": {"end_time": "2025-08-26T09:01:58.933552Z", "start_time": "2025-08-26T09:01:58.672861Z"}}, "cell_type": "code", "source": ["contract = Stock('AAPL', 'SMART', 'USD')\n", "ib.reqContractDetails(contract)"], "outputs": [{"data": {"text/plain": ["[ContractDetails(contract=Contract(secType='STK', conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS'), marketName='NMS', minTick=0.01, orderTypes='ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PRE<PERSON>GRTH,PRICECHK,<PERSON><PERSON>,<PERSON><PERSON>2<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,R<PERSON>,RTH,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>ALERST,SIZECHK,<PERSON>ARTSTG,<PERSON>NA<PERSON><PERSON>,SNAPMKT,SNAPR<PERSON>,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX', priceMagnifier=1, underConId=0, longName='APPLE INC', contractMonth='', industry='Technology', category='Computers', subcategory='Computers', timeZoneId='US/Eastern', tradingHours='20250825:0400-20250825:2000;20250826:0400-20250826:2000;20250827:0400-20250827:2000;20250828:0400-20250828:2000;20250829:0400-20250829:2000', liquidHours='20250825:0930-20250825:1600;20250826:0930-20250826:1600;20250827:0930-20250827:1600;20250828:0930-20250828:1600;20250829:0930-20250829:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26', secIdList=[TagValue(tag='ISIN', value='US0378331005')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=0.0001, sizeIncrement=0.0001, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:11:17.528723Z", "start_time": "2025-07-23T03:11:17.404004Z"}}, "cell_type": "code", "source": ["contract = Stock('AAPL', 'OVERNIGHT', 'USD')\n", "ib.reqContractDetails(contract)"], "outputs": [{"data": {"text/plain": ["[ContractDetails(contract=Contract(secType='STK', conId=265598, symbol='AAPL', exchange='OVERNIGHT', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS'), marketName='NMS', minTick=0.01, orderTypes='ACTIVETIM,AD,ALERT,ALLOC,AVGCOST,BASKET,BENCHPX,CASHQTY,DAY,DEACT,DEACTDIS,HID,LMT,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,WHATIF', validExchanges='SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX', priceMagnifier=1, underConId=0, longName='APPLE INC', contractMonth='', industry='Technology', category='Computers', subcategory='Computers', timeZoneId='US/Eastern', tradingHours='20250721:*************:0350;20250722:*************:0350;20250723:*************:0350;20250724:*************:0350;20250726:CLOSED;20250727:*************:0350', liquidHours='20250721:*************:0350;20250722:*************:0350;20250723:*************:0350;20250724:*************:0350;20250726:CLOSED;20250727:*************:0350', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=2147483647, underSymbol='', underSecType='', marketRuleIds='26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26', secIdList=[TagValue(tag='ISIN', value='US0378331005')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=0.0001, sizeIncrement=0.0001, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True) # datetime.datetime(1980, 12, 12, 14, 30)"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-17T01:23:53.764634Z", "start_time": "2025-07-17T01:23:18.646242Z"}}, "source": ["contract = Stock('AAPL', 'SMART', 'USD')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True) # datetime.datetime(1980, 12, 12, 14, 30)"], "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"cell_type": "code", "metadata": {}, "source": ["#         \"HHI.HK-20240429-HKD-FUT.HKFE\",\n", "contract = Future('HHI.HK', '202404', 'HKFE')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": "contract", "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["conids_l = [str(i) for i in conids]\n", "conids_l.sort()\n", "conids_l"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-08-26T09:02:07.561155Z", "start_time": "2025-08-26T09:02:06.545288Z"}}, "source": ["conids = [265598,\n", "          # 800322861\n", "          ]\n", "for i in conids:\n", "        contract = Stock(conId=i)\n", "        # ib.reqContractDetails(contract)\n", "        ib.qualifyContracts(contract)\n", "        from datetime import datetime\n", "        from zoneinfo import ZoneInfo\n", "        US_Eastern = ZoneInfo('America/New_York')\n", "        bars = ib.reqHistoricalData(\n", "                contract,\n", "                # endDateTime='20250617 05:00:00',\n", "                endDateTime=datetime(2025, 8, 16, 16, 0, tzinfo=US_Eastern),\n", "                durationStr='10 D',\n", "                barSizeSetting='1 day',\n", "                whatToShow='TRADES',\n", "                useRTH=True,\n", "                formatDate=1)\n", "\n", "        df = util.df(bars)\n", "        display(df)"], "outputs": [{"data": {"text/plain": ["         date    open    high     low   close      volume  average  barCount\n", "0  2025-08-04  204.46  207.88  201.67  203.35  41555468.0  204.574    220491\n", "1  2025-08-05  203.40  205.34  202.16  202.92  26016526.0  203.753    136913\n", "2  2025-08-06  205.63  215.38  205.59  213.25  68984952.0  212.616    349117\n", "3  2025-08-07  218.89  220.85  216.58  220.03  60501283.0  219.221    307682\n", "4  2025-08-08  220.86  231.00  219.25  229.35  81284112.0  227.114    386957\n", "5  2025-08-11  227.93  229.56  224.76  227.18  37453042.0  227.333    189203\n", "6  2025-08-12  228.01  230.80  227.07  229.65  31557421.0  229.283    163580\n", "7  2025-08-13  231.07  235.00  230.43  233.33  43486833.0  232.717    230760\n", "8  2025-08-14  234.01  235.06  230.85  232.78  29339367.0  232.671    154150\n", "9  2025-08-15  234.00  234.28  229.36  231.59  27981915.0  231.742    139290"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-08-04</td>\n", "      <td>204.46</td>\n", "      <td>207.88</td>\n", "      <td>201.67</td>\n", "      <td>203.35</td>\n", "      <td>41555468.0</td>\n", "      <td>204.574</td>\n", "      <td>220491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-08-05</td>\n", "      <td>203.40</td>\n", "      <td>205.34</td>\n", "      <td>202.16</td>\n", "      <td>202.92</td>\n", "      <td>26016526.0</td>\n", "      <td>203.753</td>\n", "      <td>136913</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-08-06</td>\n", "      <td>205.63</td>\n", "      <td>215.38</td>\n", "      <td>205.59</td>\n", "      <td>213.25</td>\n", "      <td>68984952.0</td>\n", "      <td>212.616</td>\n", "      <td>349117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-08-07</td>\n", "      <td>218.89</td>\n", "      <td>220.85</td>\n", "      <td>216.58</td>\n", "      <td>220.03</td>\n", "      <td>60501283.0</td>\n", "      <td>219.221</td>\n", "      <td>307682</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-08-08</td>\n", "      <td>220.86</td>\n", "      <td>231.00</td>\n", "      <td>219.25</td>\n", "      <td>229.35</td>\n", "      <td>81284112.0</td>\n", "      <td>227.114</td>\n", "      <td>386957</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-08-11</td>\n", "      <td>227.93</td>\n", "      <td>229.56</td>\n", "      <td>224.76</td>\n", "      <td>227.18</td>\n", "      <td>37453042.0</td>\n", "      <td>227.333</td>\n", "      <td>189203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-08-12</td>\n", "      <td>228.01</td>\n", "      <td>230.80</td>\n", "      <td>227.07</td>\n", "      <td>229.65</td>\n", "      <td>31557421.0</td>\n", "      <td>229.283</td>\n", "      <td>163580</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-08-13</td>\n", "      <td>231.07</td>\n", "      <td>235.00</td>\n", "      <td>230.43</td>\n", "      <td>233.33</td>\n", "      <td>43486833.0</td>\n", "      <td>232.717</td>\n", "      <td>230760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-08-14</td>\n", "      <td>234.01</td>\n", "      <td>235.06</td>\n", "      <td>230.85</td>\n", "      <td>232.78</td>\n", "      <td>29339367.0</td>\n", "      <td>232.671</td>\n", "      <td>154150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-08-15</td>\n", "      <td>234.00</td>\n", "      <td>234.28</td>\n", "      <td>229.36</td>\n", "      <td>231.59</td>\n", "      <td>27981915.0</td>\n", "      <td>231.742</td>\n", "      <td>139290</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-22T10:48:18.035416Z", "start_time": "2025-07-22T10:48:17.416792Z"}}, "cell_type": "code", "source": ["conids = [800322861]\n", "for i in conids:\n", "        contract = Stock(conId=i)\n", "        # ib.reqContractDetails(contract)\n", "        ib.qualifyContracts(contract)\n", "        from datetime import datetime\n", "        from zoneinfo import ZoneInfo\n", "        US_Eastern = ZoneInfo('America/New_York')\n", "        bars = ib.reqHistoricalData(\n", "                contract,\n", "                # endDateTime='20250617 05:00:00',\n", "                endDateTime=datetime(2025, 7, 22, 16, 0, tzinfo=US_Eastern),\n", "                durationStr='10 D',\n", "                barSizeSetting='1 day',\n", "                whatToShow='MIDPOINT',\n", "                useRTH=True,\n", "                formatDate=1)\n", "\n", "        df = util.df(bars)\n", "        display(df)"], "outputs": [{"data": {"text/plain": ["         date   open   high    low  close  volume  average  barCount\n", "0  2025-07-08  16.74  18.36  15.90  17.36    -1.0     -1.0        -1\n", "1  2025-07-09  16.38  16.60  15.56  15.98    -1.0     -1.0        -1\n", "2  2025-07-10  16.44  17.96  16.12  16.18    -1.0     -1.0        -1\n", "3  2025-07-11  16.82  17.48  16.50  16.64    -1.0     -1.0        -1\n", "4  2025-07-14  16.42  16.54  14.26  15.30    -1.0     -1.0        -1\n", "5  2025-07-15  15.66  15.92  15.00  15.22    -1.0     -1.0        -1\n", "6  2025-07-16  15.18  15.32  14.50  14.70    -1.0     -1.0        -1\n", "7  2025-07-17  12.48  12.60   8.24   9.40    -1.0     -1.0        -1\n", "8  2025-07-18   7.54   7.76   6.80   7.30    -1.0     -1.0        -1\n", "9  2025-07-21   6.71   7.15   4.48   7.10    -1.0     -1.0        -1"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-07-08</td>\n", "      <td>16.74</td>\n", "      <td>18.36</td>\n", "      <td>15.90</td>\n", "      <td>17.36</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-07-09</td>\n", "      <td>16.38</td>\n", "      <td>16.60</td>\n", "      <td>15.56</td>\n", "      <td>15.98</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-07-10</td>\n", "      <td>16.44</td>\n", "      <td>17.96</td>\n", "      <td>16.12</td>\n", "      <td>16.18</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-07-11</td>\n", "      <td>16.82</td>\n", "      <td>17.48</td>\n", "      <td>16.50</td>\n", "      <td>16.64</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-07-14</td>\n", "      <td>16.42</td>\n", "      <td>16.54</td>\n", "      <td>14.26</td>\n", "      <td>15.30</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-07-15</td>\n", "      <td>15.66</td>\n", "      <td>15.92</td>\n", "      <td>15.00</td>\n", "      <td>15.22</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-07-16</td>\n", "      <td>15.18</td>\n", "      <td>15.32</td>\n", "      <td>14.50</td>\n", "      <td>14.70</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-07-17</td>\n", "      <td>12.48</td>\n", "      <td>12.60</td>\n", "      <td>8.24</td>\n", "      <td>9.40</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-07-18</td>\n", "      <td>7.54</td>\n", "      <td>7.76</td>\n", "      <td>6.80</td>\n", "      <td>7.30</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-07-21</td>\n", "      <td>6.71</td>\n", "      <td>7.15</td>\n", "      <td>4.48</td>\n", "      <td>7.10</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 5}, {"metadata": {}, "cell_type": "code", "source": ["conid = conids[-7]\n", "print(conid)\n", "contract = Stock(conId=conid)\n", "# ib.reqContractDetails(contract)\n", "ib.qualifyContracts(contract)\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "US_Eastern = ZoneInfo('America/New_York')\n", "bars = ib.reqHistoricalData(\n", "        contract,\n", "        # endDateTime='20250617 05:00:00',\n", "        endDateTime=datetime(2025, 7, 7, 16, 0, tzinfo=US_Eastern),\n", "        durationStr='10 D',\n", "        barSizeSetting='1 day',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)\n", "\n", "df = util.df(bars)\n", "display(df)"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["ib.qualifyContracts(contract)\n", "contract"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {}, "source": "To request hourly data of the last 60 trading days:"}, {"cell_type": "code", "metadata": {}, "source": ["from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "US_Eastern = ZoneInfo('America/New_York')\n", "bars = ib.reqHistoricalData(\n", "        contract,\n", "        # endDateTime='20250617 05:00:00',\n", "        endDateTime=datetime(2025, 7, 7, 16, 0, tzinfo=US_Eastern),\n", "        durationStr='20 D',\n", "        barSizeSetting='1 day',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)\n", "\n", "df = util.df(bars)\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='20250707 17:00:00',\n", "        durationStr='1 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=False,\n", "        formatDate=1)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:30:50.607346Z", "start_time": "2024-04-10T07:30:50.435880Z"}}, "source": "Convert the list of bars to a data frame and print the first and last rows:"}, {"cell_type": "code", "metadata": {}, "source": ["df = util.df(bars)\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["        # \"HHI.HK-20241230-HKD-FUT.HKFE\",\n", "# contract = Future('HHI.HK', '202405', 'HKFE')\n", "contract = Stock(conId=8314)\n", "ib.qualifyContracts(contract)\n", "HeadTimeStamp = ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)\n", "HeadTimeStamp"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='5 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["df = util.df(bars)\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": "df", "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["contract = Future('ES', '202406', 'CME')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='20240403 17:00:00',\n", "        durationStr='10 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["df = util.df(bars)\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["contract = Future('ES', '202412', 'CME')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='10 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {}, "source": ["df = util.df(bars)\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:06:29.472574Z", "start_time": "2024-04-10T07:06:29.364183Z"}}, "source": "Instruct the notebook to draw plot graphics inline:"}, {"cell_type": "code", "metadata": {}, "source": "%matplotlib inline", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:06:32.382542Z", "start_time": "2024-04-10T07:06:31.994051Z"}, "scrolled": true}, "source": "Plot the close data"}, {"cell_type": "code", "metadata": {}, "source": "df.plot(y='close');", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:07:52.549074Z", "start_time": "2024-04-10T07:07:52.107168Z"}}, "source": "There is also a utility function to plot bars as a candlestick plot. It can accept either a DataFrame or a list of bars. Here it will print the last 100 bars:"}, {"cell_type": "code", "metadata": {}, "source": "util.barplot(bars[-100:], title=contract.symbol);", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:03.969650Z", "start_time": "2024-04-10T07:07:53.937492Z"}, "scrolled": true}, "source": ["## Historical data with realtime updates\n", "\n", "A new feature of the API is to get live updates for historical bars. This is done by setting `endDateTime` to an empty string and the `keepUpToDate` parameter to `True`.\n", "\n", "Let's get some bars with an keepUpToDate subscription:"]}, {"cell_type": "code", "metadata": {}, "source": ["contract = Forex('EURUSD')\n", "# contract = Stock('TSLA', 'SMART', 'USD')\n", "# contract = Future('ES', '202409', 'CME')\n", "bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='90 S',\n", "        barSizeSetting='10 secs',\n", "        whatToShow='MIDPOINT',\n", "        useRTH=True,\n", "        formatDate=1,\n", "        keepUpToDate=True)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:07.076916Z", "start_time": "2024-04-10T07:08:07.058915Z"}}, "source": "Replot for every change of the last bar:"}, {"cell_type": "code", "metadata": {}, "source": ["from IPython.display import display, clear_output\n", "import matplotlib.pyplot as plt\n", "\n", "def onBarUpdate(bars, hasNewBar):\n", "    plt.close()\n", "    plot = util.barplot(bars)\n", "    clear_output(wait=True)\n", "    display(plot)\n", "\n", "bars.updateEvent += onBarUpdate\n", "\n", "ib.sleep(10)\n", "ib.cancelHistoricalData(bars)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {}, "source": ["Realtime bars\n", "------------------\n", "\n", "With ``reqRealTimeBars`` a subscription is started that sends a new bar every 5 seconds.\n", "\n", "First we'll set up a event handler for bar updates:"]}, {"cell_type": "code", "metadata": {}, "source": ["def onBarUpdate(bars, hasNewBar):\n", "    print(bars[-1])"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {}, "source": "Then do the real request and connect the event handler,"}, {"cell_type": "code", "metadata": {}, "source": ["bars = ib.reqRealTimeBars(contract, 5, 'MIDPOINT', False)\n", "bars.updateEvent += onBarUpdate"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:12.038481Z", "start_time": "2024-04-10T07:08:12.029973Z"}}, "source": "let it run for half a minute and then cancel the realtime bars."}, {"cell_type": "code", "metadata": {}, "source": ["ib.sleep(30)\n", "ib.cancelRealTimeBars(bars)"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["The advantage of reqRealTimeBars is that it behaves more robust when the connection to the IB server farms is interrupted. After the connection is restored, the bars from during the network outage will be backfilled and the live bars will resume.\n", "\n", "reqHistoricalData + keepUpToDate will, at the moment of writing, leave the whole API inoperable after a network interruption."]}, {"metadata": {}, "cell_type": "code", "source": "ib.disconnect()", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}