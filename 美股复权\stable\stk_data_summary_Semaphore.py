import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.utility import load_json
from vnpy_mysql.mysql_database import DbBarData, DbBarOverview, MysqlDatabase, DB_TZ, BarOverview, BarData
from openpyxl import load_workbook
import asyncio
import talib
from typing import List, Dict, Tuple, Any, Optional, Set
import os, sys
from functools import lru_cache
import typer
from loguru import logger
from enum import Enum
import traceback
from openpyxl.utils import get_column_letter
from peewee import fn

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.excel_utils import auto_adjust_worksheet, format_number
from utils.database_manager import dbtz_convert, db_manager, IbContractDetail, IbProduct, IbFundamentals, ContractTime, IbShortStock, YahooInfo, WindStock, FutuProduct, YahooQuote, IbQuote, TZ_AMERICA_NEWYORK
from ib_.update_ib_contract_details import contract_details_to_dict
from ftplib import FTP
from ib_async import IB, Contract, ContractDetails, TagValue
import json
from firstrate_.update_conid import get_conid_mapping

# 动态生成调度器日志文件名，并添加一个过滤器，只处理来自本模块的日志
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)

START_DATE: datetime = datetime(2020, 1, 1).replace(tzinfo=DB_TZ)
from tqdm import tqdm
import shutil

# 股票类型白名单
STOCK_TYPE_WHITELIST = [
    'COMMON', 'REIT', 'CLOSED-END FUND', 'ADR', 'ROYALTY TRST', 'NY REG SHRS', 'TRACKING STK'
]
'''
黑名单
'PREFERRED', 'MLP', 'UNIT', 'US DOMESTIC', 'RIGHT', 'LTD PART', 'CONVPREFERRED', 'EQUITY WRT'

以下一般为PINK或VALUE
'RECEIPT', 'OPEN-END FUND','PREFERENCE', 'STAPLED SECURITY', 'CANADIAN DR', 'MISC.', 'GDR', 'CDI'
'''
# 设置全局变量
INTERVAL: Interval = Interval.DAILY
EXCEL_FILE: str = '美股品种信息.xlsx'
CSV_FILE: str = 'scanner_unique_stk_us.csv'
CURRENCY: str = "USD"
TURNOVER_UNIT: int = 1_000_000  # 百万USD
MAX_WORKERS: int = 50  # 最大并发数

# FTP服务器信息
FTP_SERVER: str = 'ftp2.interactivebrokers.com'
FTP_USER: str = 'shortstock'
FTP_PASS: str = ''
FTP_FILE: str = 'usa.txt'

# 定义周期
PERIODS: Dict[str, relativedelta] = {
    '1M': relativedelta(months=1),
    '3M': relativedelta(months=3),
    '6M': relativedelta(months=6),
    '1Y': relativedelta(years=1),
    '2Y': relativedelta(years=2),
    # '3Y': relativedelta(years=3)
}

PERIOD_NAMES: List[str] = list(PERIODS.keys())

class StockDataSummary:

    def __init__(self, end_date: Optional[datetime] = None, save_option: str = 'all', offset: int = 0, target_conids: Optional[List[int]] = None, use_hisfix: bool = False):
        self.database: MysqlDatabase = MysqlDatabase()
        self.end_date: datetime = end_date.replace(tzinfo=DB_TZ) if end_date else datetime.now(DB_TZ)
        self.ib: Optional[IB] = None
        self.save_option: str = save_option
        self.offset: int = offset
        self.target_conids: Optional[Set[int]] = set(target_conids) if target_conids else None
        self.use_hisfix: bool = use_hisfix
        self.usa_data: pd.DataFrame = self.load_usa_data()
        self.ftp_data_available: bool = not self.usa_data.empty
        self.COLUMNS = [
            'ID', 'conid', 'conid_db', 'isin',  # 只保留ISIN
            'symbol', 'TRADING', '剔除', '成交额>1500', '券池>100', '无相同实体', '类型白名单', 'stock_type', '中文名_futu', '中文名_wind', '名称_ib', 'local_symbol', 'assoc_entity_id',  # IbProduct字段
            
            # 财务指标（按重要性排序）
            '市值（亿美元）', '市值（亿美元）_ib', '市值（亿美元）_yahoo',
            '总收入（亿美元）', '总收入（亿美元）_ib', '总收入（亿美元）_yahoo',
            '总股本（亿）', '总股本（亿）_ib', '总股本（亿）_wind', '总股本（亿）_yahoo',
            '流通股本（亿）', '流通股本（亿）_ib', '流通股本（亿）_yahoo',
            '流通股本占总股本比例（%）',
            '市盈率', '市盈率_ib', '市盈率_yahoo', '市盈率_wind',
            '市净率', '市净率_ib', '市净率_yahoo', '市净率_wind',
            '股息收益率（%）', '股息收益率（%）_yahoo', '股息收益率（%）_wind',
            
            '毛利（亿美元）_yahoo', '总现金（亿美元）_yahoo', '总债务（亿美元）_yahoo',
            '换手率（%）_wind',
            
            # 融券相关字段（仅当ftp_data_available为True时添加）
            '月均现金利率-融券费率（%）',
            '月均融券费率（%）', 
            '月均融券池',

            '空头占总股本比例（%）_yahoo', '空头占流通股本比例（%）_yahoo',
            '空头比率（空头持仓/日成交量）_yahoo',
            '推荐评级_yahoo', '目标均价_yahoo', '目标中位价_yahoo','分析师数量_yahoo',
            
            # 分类信息
            '所属指数_ib',
            'industry', 'category', 'subcategory',
            'min_tick', 
            
            # 交易所相关
            '交易所_ib', '交易所_wind',
            '主交易所_ib', '可选交易所_ib',
            
            # 上市时间相关字段（按优先级顺序）
            '上市时间',  # 用于排序的上市时间
            '上市时间_ib',  # IB head time
            '上市时间_yahoo',  # Yahoo first trade date
            '上市时间_wind',  # Wind IPO date
            '上市时间_futu',  # Futu list time
            '起始时间_db',  # 起始时间_db
            '结束时间_db',  # 新增结束时间_db
            
            # 其他
            '证券状态(上市L退市D新N)_wind',
            '停牌天数_wind',
            '记录存在_futu', '收录复权_futu',
            '日均成交额（万美元）_1M',  # 新增的日均成交额（万美元）_1M列
            '收盘价_1M',  # 新增的收盘价_1M列
            '月均中间价_ib', '绝对盘口_ib', '相对盘口（1/2000）_ib', '相对盘口（万一）_ib', '盘口跳数_ib',
            '月均中间价_yahoo', '绝对盘口_yahoo', '相对盘口（1/2000）_yahoo', '相对盘口（万一）_yahoo', '盘口跳数_yahoo',
        ]
        if self.ftp_data_available:
            # 在空头占总股本比例（%）之前插入融券相关字段
            idx = self.COLUMNS.index('空头占总股本比例（%）_yahoo')
            self.COLUMNS[idx:idx] = ['现金利率-融券费率（%）', '融券费率（%）', '融券池']

        # 初始化数据缓存
        self.contract_details_cache: Dict[int, Dict] = {}  # 存储合约详情的字典缓存
        self.new_contract_details: Dict[int, ContractDetails] = {}  # 存储需要保存到数据库的新合约详情，key为conid
        self.ib_products: Dict[int, Dict] = {}  # conid -> product
        self.ib_fundamentals: Dict[int, Dict] = {}  # conid -> fundamentals
        self.contract_times: Dict[int, Dict] = {}  # conid -> times
        self.yahoo_infos: Dict[str, Dict] = {}  # isin -> info
        self.wind_stocks: Dict[str, Dict] = {}  # isin -> stock
        self.futu_products: Dict[str, Dict] = {}  # symbol -> product
        self.bar_overviews: Set[str] = set()  # conid with bar data
        self.ib_avg_quotes: Dict[int, Dict] = {}  # conid with ib quotes
        self.max_id: int = 0  # 存储现有记录的最大ID

        # 初始化时加载所有数据
        self._load_all_data()

    async def connect_ib(self):
        # 从connect_ib.json读取连接信息
        connect_filename = 'connect_ib.json'
        setting = load_json(connect_filename)

        ip = setting.get("TWS地址", '127.0.0.1')
        port = setting.get("TWS端口", 4002)
        client_id = setting.get("客户号", 800) + self.offset

        self.ib = IB()
        await self.ib.connectAsync(ip, port, clientId=client_id)

    async def disconnect_ib(self):
        if self.ib:
            self.ib.disconnect()

    async def get_contract_details(self, conid: int) -> Dict:
        """获取合约详情，优先从缓存获取，如果没有则从IB获取"""
        # 从缓存获取
        if conid in self.contract_details_cache:
            # logger.debug(f"从缓存获取合约 {conid} 的详情")
            return self.contract_details_cache[conid]
        
        # 从IB获取
        try:
            # logger.debug(f"从IB获取合约 {conid} 的详情")
            contract = Contract(conId=conid)
            details_list = await self.ib.reqContractDetailsAsync(contract)
            
            if not details_list:
                # logger.warning(f"未能从IB获取到合约 {conid} 的详情")
                return {}
            
            details = details_list[0]
            
            # 构造需要的字段字典（用于返回和缓存）
            details_dict = {
                'symbol': details.contract.symbol,
                'exchange': details.contract.exchange,
                'primary_exchange': details.contract.primaryExchange,
                'local_symbol': details.contract.localSymbol,
                'description': details.contract.description,
                'min_tick': details.minTick,
                'size_increment': details.sizeIncrement,
                'industry': details.industry,
                'category': details.category,
                'subcategory': details.subcategory,
                'time_zone_id': details.timeZoneId,
                'stock_type': details.stockType
            }
            
            # 缓存结果
            self.contract_details_cache[conid] = details_dict
            
            # 准备保存到数据库的完整合约详情
            self.new_contract_details[conid] = details
            
            return details_dict
            
        except Exception as e:
            logger.error(f"获取合约 {conid} 详情时发生错误: {str(e)}")
            return {}

    def load_usa_data(self) -> pd.DataFrame:
        """从FTP服务器下载usa.txt并加载数据，如果本地已有当天的文件则直接使用"""
        try:
            # 检查本地文件是否存在且是当天修改的
            if os.path.exists(FTP_FILE):
                file_modification_time = datetime.fromtimestamp(os.path.getmtime(FTP_FILE)) # getctime 创建时间 getmtime 修改时间
                today = datetime.now().date()
                if file_modification_time.date() == today:
                    logger.info(f"使用本地{FTP_FILE}文件...")
                    # 读取usa.txt文件，忽略最后一列
                    df = pd.read_csv(FTP_FILE, sep='|', skiprows=2, skipfooter=1, engine='python', usecols=range(8))
                    df.columns = ['symbol', 'currency', 'name', 'conid', 'isin', '现金利率-融券费率（%）', '融券费率（%）', '融券池']
                    return df

            logger.info(f"正在从FTP服务器下载{FTP_FILE}...")
            
            with FTP(FTP_SERVER) as ftp:
                ftp.login(user=FTP_USER, passwd=FTP_PASS)
                
                with open(FTP_FILE, 'wb') as file:
                    ftp.retrbinary(f'RETR {FTP_FILE}', file.write)

            logger.info(f"{FTP_FILE}下载完成。")

            # 读取usa.txt文件，忽略最后一列
            df = pd.read_csv(FTP_FILE, sep='|', skiprows=2, skipfooter=1, engine='python', usecols=range(8))
            df.columns = ['symbol', 'currency', 'name', 'conid', 'isin', '现金利率-融券费率（%）', '融券费率（%）', '融券池']
            return df
        except Exception as e:
            logger.warning(f"FTP服务器访问失败: {str(e)}，将继续运行但不包含FTP相关数据。")
            return pd.DataFrame(columns=['symbol', 'currency', 'name', 'conid', 'isin', '现金利率-融券费率（%）', '融券费率（%）', '融券池'])

    @lru_cache(maxsize=999)
    def load_bar_data(self, conid: str, exchange: str, start: datetime, end: datetime) -> List[BarData]:
        return self.database.load_bar_data(conid, Exchange(exchange), INTERVAL, start=start, end=end)

    def load_first_bar(self, conid: str, exchange: str) -> Optional[BarData]:
        """加载指定标的的第一个bar数据"""
        for overview in self.bar_overviews:
            if overview.symbol == conid and overview.exchange.value == exchange and overview.interval == INTERVAL:
                start = overview.start.replace(tzinfo=DB_TZ)
                start = max(start, START_DATE)
                bars = self.load_bar_data(conid, exchange, start, start + relativedelta(days=1))
                if bars:
                    return bars[0]
        return None

    def load_last_bar(self, conid: str, exchange: str) -> Optional[BarData]:
        """加载指定标的的最后一个bar数据"""
        for overview in self.bar_overviews:
            if overview.symbol == conid and overview.exchange.value == exchange and overview.interval == INTERVAL:
                end = overview.end.replace(tzinfo=DB_TZ)
                bars = self.load_bar_data(conid, exchange, end - relativedelta(days=1), end)
                if bars:
                    return bars[-1]
        return None

    async def get_all_symbols(self) -> List[Tuple[int, str]]:
        """获取所有最新的IB产品"""
        # 获取最新的 created_time
        latest_record = IbProduct.select(IbProduct.created_time).order_by(IbProduct.created_time.desc()).first()
        latest_time = latest_record.created_time if latest_record else datetime.now()
        
        # 只获取最新创建的 IbProduct 记录的 conid 和 symbol
        products = IbProduct.select(IbProduct.conid, IbProduct.symbol).where(
            (IbProduct.is_latest == True) &
            (IbProduct.created_time >= latest_time.replace(hour=0, minute=0, second=0, microsecond=0))
        )
        
        symbols = []
        for product in products:
            # 过滤掉以.OLD或.OLD1或 WI结尾的symbol
            if product.symbol and (product.symbol.endswith('.OLD') or product.symbol.endswith('.OLD1') or product.symbol.endswith(' WI')):
                logger.info(f"跳过以.OLD或.OLD1或 WI结尾的conid: {product.conid}, symbol: {product.symbol}")
                continue
                
            # 使用get_contract_details获取exchange信息（会自动处理缓存和请求）
            details = await self.get_contract_details(product.conid)
            if not details:
                # logger.warning(f"无法获取conid {product.conid}的exchange信息")
                continue
                
            # 检查是否为VALUE交易所或primary_exchange为PINK，如果是则跳过
            if 'VALUE' == details.get('valid_exchanges') or 'PINK' == details.get('primary_exchange'):
                # logger.info(f"跳过VALUE交易所或PINK市场标的: {product.conid}")
                continue
            
            # 如果白名单不为空，检查股票类型是否在白名单中
            # if STOCK_TYPE_WHITELIST and details.get('stock_type') not in STOCK_TYPE_WHITELIST:
            #     # logger.info(f"跳过不在白名单中的股票类型: {product.conid}, stock_type: {details.get('stock_type')}")
            #     continue
                
            if details.get('exchange'):
                symbols.append((product.conid, details['exchange']))
        
        # 如果指定了target_conids，在最后过滤只保留这些conid
        if self.target_conids:
            original_count = len(symbols)
            symbols = [(conid, exchange) for conid, exchange in symbols if conid in self.target_conids]
            logger.info(f"指定conids过滤: 从 {original_count} 个标的过滤到 {len(symbols)} 个指定标的")
            for conid, exchange in symbols:
                logger.info(f"处理指定conid: {conid}, exchange: {exchange}")
            
        return symbols

    def get_db_symbols(self) -> List[Tuple[str, str]]:
        """获取数据库中所有的标的，只返回在end_date之前上市的标的"""
        return [
            (overview.symbol, overview.exchange.value) 
            for overview in self.bar_overviews 
            if overview.interval == INTERVAL and overview.start.replace(tzinfo=DB_TZ) <= self.end_date
        ]

    def get_latest_trading_symbols(self) -> List[Tuple[str, str]]:
        """获取最新交易日有历史数据的股票列表，日期小于等于end_date"""
        latest_date = min(max(overview.end for overview in self.bar_overviews), self.end_date.replace(tzinfo=None))
        logger.debug(f"Initial latest_date: {latest_date}")

        # 从最新数据日期开始往前逐一减一天，直到找到一个交易日
        while True:
            trading_symbols = [
                (overview.symbol, overview.exchange.value)
                for overview in self.bar_overviews
                if overview.start <= latest_date <= overview.end
            ]
            if len(trading_symbols) > 1000:
                break
            latest_date -= timedelta(days=1)
            logger.debug(f"Updated latest_date: {latest_date}")
        assert trading_symbols, "没有找到任何有历史数据的股票"
        return trading_symbols

    def _load_all_data(self):
        """一次性加载所有需要的数据"""
        logger.info("正在加载所有数据...")

        # 加载合约详情数据
        logger.info("从数据库加载合约详情...")
        for db_details in IbContractDetail.select(
            IbContractDetail.conid,
            IbContractDetail.symbol,
            IbContractDetail.exchange,
            IbContractDetail.primary_exchange,
            IbContractDetail.local_symbol,
            IbContractDetail.min_tick,
            IbContractDetail.size_increment,
            IbContractDetail.industry,
            IbContractDetail.category,
            IbContractDetail.subcategory,
            IbContractDetail.time_zone_id,
            IbContractDetail.stock_type,
            IbContractDetail.sec_id_list,
            IbContractDetail.valid_exchanges
        ):
            details_dict = {
                'symbol': db_details.symbol,
                'exchange': db_details.exchange,
                'primary_exchange': db_details.primary_exchange,
                'local_symbol': db_details.local_symbol,
                'min_tick': db_details.min_tick,
                'size_increment': db_details.size_increment,
                'industry': db_details.industry,
                'category': db_details.category,
                'subcategory': db_details.subcategory,
                'time_zone_id': db_details.time_zone_id,
                'stock_type': db_details.stock_type,
                'valid_exchanges': db_details.valid_exchanges
            }
            
            self.contract_details_cache[db_details.conid] = details_dict
        
        logger.info(f"已加载 {len(self.contract_details_cache)} 个IbContractDetail到缓存")

        # 加载IB产品数据
        for product in (IbProduct
                       .select(IbProduct.conid, IbProduct.symbol, IbProduct.local_symbol, 
                              IbProduct.description, IbProduct.isin, IbProduct.assoc_entity_id)
                       .where(IbProduct.is_latest == True)):
            self.ib_products[product.conid] = {
                'symbol': product.symbol,
                'local_symbol': product.local_symbol,
                'description': product.description,
                'isin': product.isin,
                'assoc_entity_id': product.assoc_entity_id
            }

        logger.info(f"已加载 {len(self.ib_products)} 个IbProduct到缓存")

        # 加载IB基本面数据
        for fund in (IbFundamentals
                    .select(IbFundamentals.conid, IbFundamentals.shares_out, IbFundamentals.total_float,
                           IbFundamentals.mktcap, IbFundamentals.ttmrev, IbFundamentals.peexclxor,
                           IbFundamentals.price2bk, IbFundamentals.index_constituents)):
            self.ib_fundamentals[fund.conid] = {
                'shares_out': fund.shares_out,
                'total_float': fund.total_float,
                'mktcap': fund.mktcap,
                'ttmrev': fund.ttmrev,
                'peexclxor': fund.peexclxor,
                'price2bk': fund.price2bk,
                'index_constituents': fund.index_constituents
            }

        logger.info(f"已加载 {len(self.ib_fundamentals)} 个IbFundamentals到缓存")

        # 加载合约时间数据
        for time in ContractTime.select(ContractTime.conid, ContractTime.ib_head_time):
            self.contract_times[time.conid] = {
                'ib_head_time': time.ib_head_time
            }

        logger.info(f"已加载 {len(self.contract_times)} 个ContractTime到缓存")

        # 加载Yahoo信息
        for info in (YahooInfo
                    .select(YahooInfo.isin, YahooInfo.first_trade_date_milliseconds,
                           YahooInfo.target_mean_price, YahooInfo.target_median_price,
                           YahooInfo.recommendation_key, YahooInfo.number_of_analyst_opinions,
                           YahooInfo.shares_percent_shares_out, YahooInfo.short_percent_of_float,
                           YahooInfo.short_ratio, YahooInfo.gross_profits,
                           YahooInfo.total_cash, YahooInfo.total_debt, YahooInfo.dividend_yield,
                           YahooInfo.market_cap, YahooInfo.total_revenue,
                           YahooInfo.shares_outstanding, YahooInfo.float_shares,
                           YahooInfo.trailing_pe, YahooInfo.price_to_book)):
            self.yahoo_infos[info.isin] = {
                'first_trade_date_milliseconds': info.first_trade_date_milliseconds,
                'target_mean_price': info.target_mean_price,
                'target_median_price': info.target_median_price,
                'recommendation_key': info.recommendation_key,
                'number_of_analyst_opinions': info.number_of_analyst_opinions,
                'shares_percent_shares_out': info.shares_percent_shares_out,
                'short_percent_of_float': info.short_percent_of_float,
                'short_ratio': info.short_ratio,
                'gross_profits': info.gross_profits,
                'total_cash': info.total_cash,
                'total_debt': info.total_debt,
                'dividend_yield': info.dividend_yield,
                'market_cap': info.market_cap,
                'total_revenue': info.total_revenue,
                'shares_outstanding': info.shares_outstanding,
                'float_shares': info.float_shares,
                'trailing_pe': info.trailing_pe,
                'price_to_book': info.price_to_book
            }

        logger.info(f"已加载 {len(self.yahoo_infos)} 个YahooInfo到缓存")

        # 加载Wind股票数据
        for stock in (WindStock
                     .select(WindStock.isin_code, WindStock.sec_status,
                            WindStock.exch_eng, WindStock.turn, WindStock.susp_days,
                            WindStock.ipo_date, WindStock.comp_name,
                            WindStock.total_shares, WindStock.pe, WindStock.pb,
                            WindStock.dividendyield2)):
            self.wind_stocks[stock.isin_code] = {
                'sec_status': stock.sec_status,
                'exch_eng': stock.exch_eng,
                'turn': stock.turn,
                'susp_days': stock.susp_days,
                'ipo_date': stock.ipo_date,
                'comp_name': stock.comp_name,
                'total_shares': stock.total_shares,
                'pe': stock.pe,
                'pb': stock.pb,
                'dividendyield2': stock.dividendyield2
            }

        logger.info(f"已加载 {len(self.wind_stocks)} 个WindStock到缓存")

        # 加载富途产品数据
        for product in FutuProduct.select(FutuProduct.code, FutuProduct.rehab_known, FutuProduct.list_time, FutuProduct.stock_name):  # 添加stock_name字段
            self.futu_products[product.code.replace('US.', '').replace('.', ' ')] = {
                'rehab_known': product.rehab_known,
                'list_time': product.list_time,
                'stock_name': product.stock_name  # 添加stock_name到缓存
            }

        logger.info(f"已加载 {len(self.futu_products)} 个FutuProduct到缓存")

        # 加载K线概览数据
        self.bar_overviews = self.database.get_bar_overview()
        logger.info(f"已加载 {len(self.bar_overviews)} 个BarOverview到缓存")

        # 加载conid_db映射
        self.conid_db_mapping = self._get_conid_db_mapping()
        logger.info(f"已加载 {len(self.conid_db_mapping)} 个conid_db映射到缓存")

        # 计算IB盘口平均值
        self.ib_avg_quotes = self._calculate_average_ib_quotes()
        logger.info(f"已计算 {len(self.ib_avg_quotes)} 个IB盘口平均值并加载到缓存")

        # 计算Yahoo盘口平均值
        self.yahoo_avg_quotes = self._calculate_average_yahoo_quotes()
        logger.info(f"已计算 {len(self.yahoo_avg_quotes)} 个Yahoo盘口平均值并加载到缓存")

        # 加载IB做空数据并计算月均值
        self.ib_short_avg = self._calculate_average_ib_shorts()
        logger.info(f"已计算 {len(self.ib_short_avg)} 个IB做空数据月均值并加载到缓存")

        logger.info("数据加载完成")

    def _calculate_average_ib_quotes(self) -> Dict[int, Dict[str, Any]]:
        """计算最近一个月的IB盘口平均值"""
        logger.info("正在计算最近一个月的IB盘口平均值...")
        quotes_avg = {}
        try:
            # 计算一个月前的时间（基于DB_TZ）
            one_month_ago = self.end_date - relativedelta(months=1)

            query = (
                IbQuote.select(
                    IbQuote.conid,
                    fn.AVG(IbQuote.ask).alias('avg_ask'),
                    fn.AVG(IbQuote.bid).alias('avg_bid')
                )
                .where(
                    (IbQuote.datetime >= one_month_ago) &
                    (IbQuote.ask > 0) &
                    (IbQuote.bid > 0) &
                    (IbQuote.ask > IbQuote.bid) # 排除 ask_price <= bid_price 的情况
                )
                .group_by(IbQuote.conid)
            )

            for record in query:
                # 确保获取到的值不为 None，如果为 None 则设为 0 以避免计算错误
                avg_ask = float(record.avg_ask) if record.avg_ask else 0
                avg_bid = float(record.avg_bid) if record.avg_bid else 0

                # 先计算中间价
                avg_mid_price = (avg_ask + avg_bid) / 2 if (avg_ask + avg_bid) else None

                # 计算绝对价差
                avg_absolute_spread = avg_ask - avg_bid

                # 计算相对价差（1/2000）
                avg_relative_spread_1_2000 = None
                if avg_mid_price:  # 避免除以零
                    avg_relative_spread_1_2000 = (avg_absolute_spread / avg_mid_price) * 2000

                # 计算相对价差（万一）
                avg_relative_spread_wan_yi = None
                if avg_relative_spread_1_2000 is not None:
                    avg_relative_spread_wan_yi = avg_relative_spread_1_2000 * 5

                quotes_avg[record.conid] = {
                    "absolute_spread": avg_absolute_spread if avg_absolute_spread is not None else None,
                    "relative_spread_1_2000": avg_relative_spread_1_2000 if avg_relative_spread_1_2000 is not None else None,
                    "relative_spread_wan_yi": avg_relative_spread_wan_yi if avg_relative_spread_wan_yi is not None else None,
                    "mid_price": avg_mid_price if avg_mid_price is not None else None
                }
            logger.info(f"已计算 {len(quotes_avg)} 个conid的平均盘口数据。")
        except Exception as e:
            logger.error(f"计算IB盘口平均值时发生错误: {str(e)}{traceback.format_exc()}")
        return quotes_avg

    def _calculate_average_yahoo_quotes(self) -> Dict[str, Dict[str, Any]]:
        """计算最近一个月的Yahoo盘口平均值"""
        logger.info("正在计算最近一个月的Yahoo盘口平均值...")
        quotes_avg = {}
        try:
            # 计算一个月前的时间（基于DB_TZ）
            one_month_ago = self.end_date - relativedelta(months=1)

            query = (
                YahooQuote.select(
                    YahooQuote.isin,
                    fn.AVG(YahooQuote.ask).alias('avg_ask'),
                    fn.AVG(YahooQuote.bid).alias('avg_bid')
                )
                .where(
                    (YahooQuote.datetime >= one_month_ago) &
                    (YahooQuote.ask > 0) &
                    (YahooQuote.bid > 0) &
                    (YahooQuote.ask > YahooQuote.bid) # 排除 ask_price <= bid_price 的情况
                )
                .group_by(YahooQuote.isin)
            )

            for record in query:
                # 确保获取到的值不为 None，如果为 None 则设为 0 以避免计算错误
                avg_ask = float(record.avg_ask) if record.avg_ask else 0
                avg_bid = float(record.avg_bid) if record.avg_bid else 0

                # 先计算中间价
                avg_mid_price = (avg_ask + avg_bid) / 2 if (avg_ask + avg_bid) else None

                # 计算绝对价差
                avg_absolute_spread = avg_ask - avg_bid

                # 计算相对价差（1/2000）
                avg_relative_spread_1_2000 = None
                if avg_mid_price:  # 避免除以零
                    avg_relative_spread_1_2000 = (avg_absolute_spread / avg_mid_price) * 2000

                # 计算相对价差（万一）
                avg_relative_spread_wan_yi = None
                if avg_relative_spread_1_2000 is not None:
                    avg_relative_spread_wan_yi = avg_relative_spread_1_2000 * 5

                quotes_avg[record.isin] = {
                    "absolute_spread": avg_absolute_spread if avg_absolute_spread is not None else None,
                    "relative_spread_1_2000": avg_relative_spread_1_2000 if avg_relative_spread_1_2000 is not None else None,
                    "relative_spread_wan_yi": avg_relative_spread_wan_yi if avg_relative_spread_wan_yi is not None else None,
                    "mid_price": avg_mid_price if avg_mid_price is not None else None
                }
            logger.info(f"已计算 {len(quotes_avg)} 个isin的平均盘口数据。")
        except Exception as e:
            logger.error(f"计算Yahoo盘口平均值时发生错误: {str(e)}{traceback.format_exc()}")
        return quotes_avg

    def _calculate_average_ib_shorts(self) -> Dict[int, Dict[str, Any]]:
        """计算IB做空数据的月均值"""
        # 获取一个月前的日期
        one_month_ago = self.end_date - relativedelta(months=1)
        
        # 初始化结果字典
        result = {}
        
        # 使用原始SQL查询最近一个月的做空数据
        sql = """
            SELECT
                t1.conid,
                AVG(t1.rebate_rate) AS avg_rebate_rate,
                AVG(t1.fee_rate) AS avg_fee_rate,
                AVG(CAST(REPLACE(t1.available, '>', '') AS DOUBLE)) AS avg_available
            FROM
                ib_shortstock AS t1
            WHERE
                t1.datetime >= %s
            GROUP BY
                t1.conid
        """
        
        try:
            # 执行SQL查询
            cursor = db_manager.common_db.execute_sql(sql, (one_month_ago,))
            
            # 处理查询结果
            for row in cursor.fetchall():
                conid, avg_rebate_rate, avg_fee_rate, avg_available = row
                result[conid] = {
                    'avg_rebate_rate': avg_rebate_rate,
                    'avg_fee_rate': avg_fee_rate,
                    'avg_available': int(avg_available) if avg_available is not None else None
                }
            logger.info(f"已计算 {len(result)} 个IB做空数据月均值并加载到缓存")
        except Exception as e:
            logger.error(f"计算IB做空数据月均值时发生错误: {str(e)}{traceback.format_exc()}")
            
        return result

    def _get_conid_db_mapping(self) -> Dict[int, str]:
        """获取conid到conid_db的映射
        
        对于每个conid，找到end_date当天及之后最近的以"{conid}_"开头的合约
        如果找不到，就使用原始conid
        
        Returns:
            Dict[int, str]: conid到conid_db的映射
        """
        mapping = {}
        end_date_str = self.end_date.strftime('%y%m%d')
        
        # 从bar_overviews中获取所有symbol
        for overview in self.bar_overviews:
            symbol = overview.symbol
            try:
                # 尝试解析symbol
                if '_' in symbol:
                    base_conid = int(symbol.split('_')[0])
                    date_str = symbol.split('_')[1]
                    
                    # 如果这个symbol的日期大于等于end_date，且比当前记录的更接近end_date
                    if date_str >= end_date_str:
                        if base_conid not in mapping or date_str < mapping[base_conid].split('_')[1]:
                            mapping[base_conid] = symbol
            except (ValueError, IndexError):
                continue
        
        # 对于没有找到合约式复权数据的conid，使用原始conid
        for overview in self.bar_overviews:
            try:
                if '_' not in overview.symbol:
                    base_conid = int(overview.symbol)
                    if base_conid not in mapping:
                        mapping[base_conid] = str(base_conid)
            except ValueError:
                continue
        
        return mapping

    async def update_stable_ids(self, symbols: List[Tuple[int, str]]) -> pd.DataFrame:
        """更新稳定ID信息"""
        logger.info("正在准备ID数据...")
        if os.path.exists(EXCEL_FILE):
            df_existing = pd.read_excel(EXCEL_FILE, sheet_name='稳定ID')
            # df_existing['conid'] = df_existing['conid'].astype(str)
            self.max_id = df_existing['ID'].max()
        else:
            df_existing = pd.DataFrame(columns=self.COLUMNS)
            self.max_id = 0

        # 1. 更新现有记录的conid
        logger.info("正在更新现有记录的conid...")
        
        # 获取所有需要更新的conid
        old_conids = set()
        for conid in df_existing['conid'].dropna():
            old_conids.add(int(conid))
            
        # 使用优化后的get_conid_mappings函数获取映射关系
        conid_mappings, warning_mappings = get_conid_mapping(old_conids)
        
        # 如果有警告映射，输出警告信息
        if warning_mappings:
            logger.warning(f"\n发现 {len(warning_mappings)} 个链式判断错误的映射关系，这些映射将被跳过:")
            for old_conid, mapping_info in warning_mappings.items():
                logger.warning(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                             f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
            logger.warning("请人工核查这些映射关系")
        
        # 更新DataFrame中的conid
        if conid_mappings:
            logger.info(f"找到 {len(conid_mappings)} 个需要更新的conid:")
            for old_conid, mapping_info in conid_mappings.items():
                logger.info(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                          f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
                df_existing.loc[df_existing['conid'] == old_conid, 'conid'] = mapping_info['new_conid']

        # 2. 处理所有记录（包括现有和新的）
        all_data = {}  # 用于存储所有记录的处理结果，key为(conid, exchange)
        existing_symbols = set(zip(df_existing['conid'], df_existing['交易所_ib']))
        new_symbols = set(symbols) - existing_symbols
        
        # 使用信号量控制并发数
        sem = asyncio.Semaphore(MAX_WORKERS)
        
        async def process_symbol(conid: int, exchange: str, is_new: bool = False, existing_id: int = None) -> None:
            """处理单个标的的详细信息
            
            Args:
                conid: 合约ID
                exchange: 交易所
                is_new: 是否为新记录
                existing_id: 现有记录的ID（如果有）
            """
            async with sem:
                try:
                    contract_details = await self.get_contract_details(conid)
                    if not contract_details:
                        return
                        
                    # 获取基础数据
                    ib_product = self.ib_products.get(conid, {})
                    isin = ib_product.get('isin')
                    contract_time = self.contract_times.get(conid, {})
                    yahoo_info = self.yahoo_infos.get(isin, {})
                    wind_stock = self.wind_stocks.get(isin, {})
                    ib_symbol = ib_product.get('symbol', '')
                    futu_product = self.futu_products.get(ib_symbol, {})

                    # 获取各个来源的上市时间
                    ib_head_time = dbtz_convert(contract_time.get('ib_head_time'), 'UTC', 'America/New_York')
                    yahoo_time = yahoo_info.get('first_trade_date_milliseconds')
                    wind_time = wind_stock.get('ipo_date')
                    futu_time = futu_product.get('list_time')
                    futu_time = None if futu_time and futu_time.replace(tzinfo=None) == datetime(1970, 1, 1) else futu_time
                    
                    # 获取first_bar
                    first_bar = self.load_first_bar(str(conid), exchange)
                    db_time = first_bar.datetime.astimezone(TZ_AMERICA_NEWYORK).replace(tzinfo=None) if first_bar else None
                    
                    # 获取最后一条bar数据，用于填充结束时间_db
                    last_bar = self.load_last_bar(str(conid), exchange)
                    db_end_time = last_bar.datetime.astimezone(TZ_AMERICA_NEWYORK).replace(tzinfo=None) if last_bar else None
                    
                    # 按优先级获取上市时间
                    listing_time = ib_head_time
                    if listing_time:
                        if listing_time.year == 1980:
                            if yahoo_time:
                                listing_time = yahoo_time
                            elif wind_time:
                                listing_time = datetime.combine(wind_time, datetime.min.time())
                            elif futu_time:
                                listing_time = futu_time
                    elif yahoo_time:
                        listing_time = yahoo_time
                    elif wind_time:
                        # wind_time是date类型，需要转换为datetime
                        listing_time = datetime.combine(wind_time, datetime.min.time())
                    elif futu_time:
                        listing_time = futu_time
                    elif db_time:
                        listing_time = db_time
                    
                    # 如果没有任何上市时间信息，跳过这个标的
                    temp_time = listing_time
                    if not temp_time:
                        # logger.info(f"跳过标的 {conid}, {ib_symbol}: 无法获取上市时间信息")
                        # temp_time = datetime(1970, 1, 1) # test
                        return
                    else:
                        temp_time = temp_time.replace(tzinfo=None,hour=0,minute=0,second=0)
                    
                    # 检查上市时间是否在end_date之前
                    end_time = self.end_date.replace(tzinfo=None)
                    if temp_time > end_time:
                        # logger.info(f"跳过标的 {conid}, {ib_symbol}: 上市时间 {listing_time} 晚于结束时间 {end_time}")
                        return
                    
                    # 获取conid_db
                    conid_db = self.conid_db_mapping.get(conid)
                        
                    # 获取其他数据
                    ib_fund = self.ib_fundamentals.get(conid, {})

                    min_tick = contract_details.get('min_tick') # 获取最小价格跳动
                    # IB盘口数据
                    average_ib_quote = self.ib_avg_quotes.get(conid, {})
                    absolute_spread_ib = average_ib_quote.get('absolute_spread')
                    mid_price_ib = average_ib_quote.get('mid_price')
                    relative_spread_1_2000_ib = average_ib_quote.get('relative_spread_1_2000')
                    relative_spread_wan_yi_ib = average_ib_quote.get('relative_spread_wan_yi')                    
                    ticks_ib = None
                    if pd.notnull(absolute_spread_ib) and pd.notnull(min_tick) and min_tick > 0:
                        ticks_ib = absolute_spread_ib / min_tick
                    
                    # yahoo相关数据
                    average_yahoo_quote = self.yahoo_avg_quotes.get(isin, {})
                    absolute_spread_yahoo = average_yahoo_quote.get('absolute_spread')
                    mid_price_yahoo = average_yahoo_quote.get('mid_price')
                    relative_spread_1_2000_yahoo = average_yahoo_quote.get('relative_spread_1_2000')
                    relative_spread_wan_yi_yahoo = average_yahoo_quote.get('relative_spread_wan_yi')
                    ticks_yahoo = None
                    if pd.notnull(absolute_spread_yahoo) and pd.notnull(min_tick) and min_tick > 0:
                        ticks_yahoo = absolute_spread_yahoo / min_tick

                    # 先计算各数据源的值
                    market_cap_ib = ib_fund.get('mktcap', 0) / 100 if ib_fund.get('mktcap') is not None else None
                    market_cap_yahoo = yahoo_info.get('market_cap', 0) / 1e8 if yahoo_info.get('market_cap') is not None else None
                    
                    revenue_ib = ib_fund.get('ttmrev', 0) / 100 if ib_fund.get('ttmrev') is not None else None
                    revenue_yahoo = yahoo_info.get('total_revenue', 0) / 1e8 if yahoo_info.get('total_revenue') is not None else None
                    
                    shares_ib = ib_fund.get('shares_out', 0) / 1e8 if ib_fund.get('shares_out') is not None else None
                    shares_wind = wind_stock.get('total_shares', 0) / 1e8 if wind_stock.get('total_shares') is not None else None
                    shares_yahoo = yahoo_info.get('shares_outstanding', 0) / 1e8 if yahoo_info.get('shares_outstanding') is not None else None
                    
                    float_shares_ib = ib_fund.get('total_float', 0) / 1e8 if ib_fund.get('total_float') is not None else None
                    float_shares_yahoo = yahoo_info.get('float_shares', 0) / 1e8 if yahoo_info.get('float_shares') is not None else None

                    # 优先获取总股本和流通股本
                    total_shares = shares_ib or shares_wind or shares_yahoo
                    float_shares = float_shares_ib or float_shares_yahoo

                    # 计算流通股本占总股本比例
                    float_to_total_ratio = None
                    if total_shares is not None and total_shares != 0 and float_shares is not None:
                        float_to_total_ratio = (float_shares / total_shares) * 100

                    pe_ib = ib_fund.get('peexclxor')
                    pe_yahoo = yahoo_info.get('trailing_pe')
                    pe_wind = wind_stock.get('pe')
                    
                    pb_ib = ib_fund.get('price2bk')
                    pb_yahoo = yahoo_info.get('price_to_book')
                    pb_wind = wind_stock.get('pb')
                    
                    dividend_yahoo = yahoo_info.get('dividend_yield')
                    dividend_wind = wind_stock.get('dividendyield2')
                    
                    exchange_ib = contract_details.get('exchange')
                    exchange_wind = wind_stock.get('exch_eng')
                    
                    # 获取IB做空数据
                    ib_short = self.ib_short_avg.get(conid, {})
                    
                    # 计算新增的辅助列
                    stock_type_value = contract_details.get('stock_type')
                    whitelist_status = 1 if stock_type_value in STOCK_TYPE_WHITELIST else None if not STOCK_TYPE_WHITELIST else None
                    
                    data_dict = {
                        'conid': conid,
                        'conid_db': conid_db,
                        'isin': isin,
                        'symbol': ib_product.get('symbol'),
                        '剔除': None,
                        '成交额>1500': None,  # 将在后面设置
                        '券池>100': None,  # 将在后面设置
                        '无相同实体': None,  # 将在后面设置
                        '类型白名单': whitelist_status,
                        'stock_type': stock_type_value,
                        '中文名_futu': futu_product.get('stock_name'),
                        '中文名_wind': wind_stock.get('comp_name'),
                        '名称_ib': ib_product.get('description'),
                        'local_symbol': ib_product.get('local_symbol'),
                        'assoc_entity_id': ib_product.get('assoc_entity_id'),
                        
                        # 财务指标
                        '市值（亿美元）': market_cap_ib or market_cap_yahoo,
                        '市值（亿美元）_ib': market_cap_ib,
                        '市值（亿美元）_yahoo': market_cap_yahoo,
                        
                        '总收入（亿美元）': revenue_ib or revenue_yahoo,
                        '总收入（亿美元）_ib': revenue_ib,
                        '总收入（亿美元）_yahoo': revenue_yahoo,
                        
                        '总股本（亿）': total_shares,
                        '总股本（亿）_ib': shares_ib,
                        '总股本（亿）_wind': shares_wind,
                        '总股本（亿）_yahoo': shares_yahoo,
                        
                        '流通股本（亿）': float_shares,
                        '流通股本（亿）_ib': float_shares_ib,
                        '流通股本（亿）_yahoo': float_shares_yahoo,
                        
                        # 流通股本占总股本比例
                        '流通股本占总股本比例（%）': float_to_total_ratio,
                        
                        '市盈率': pe_ib or pe_yahoo or pe_wind,
                        '市盈率_ib': pe_ib,
                        '市盈率_yahoo': pe_yahoo,
                        '市盈率_wind': pe_wind,
                        
                        '市净率': pb_ib or pb_yahoo or pb_wind,
                        '市净率_ib': pb_ib,
                        '市净率_yahoo': pb_yahoo,
                        '市净率_wind': pb_wind,
                        
                        '股息收益率（%）': dividend_yahoo or dividend_wind,
                        '股息收益率（%）_yahoo': dividend_yahoo,
                        '股息收益率（%）_wind': dividend_wind,
                        
                        '毛利（亿美元）_yahoo': yahoo_info.get('gross_profits', 0) / 1e8 if yahoo_info.get('gross_profits') is not None else None,
                        '总现金（亿美元）_yahoo': yahoo_info.get('total_cash', 0) / 1e8 if yahoo_info.get('total_cash') is not None else None,
                        '总债务（亿美元）_yahoo': yahoo_info.get('total_debt', 0) / 1e8 if yahoo_info.get('total_debt') is not None else None,
                        
                        '换手率（%）_wind': wind_stock.get('turn'),

                        '月均现金利率-融券费率（%）': ib_short.get('avg_rebate_rate'),
                        '月均融券费率（%）': ib_short.get('avg_fee_rate'),
                        '月均融券池': ib_short.get('avg_available'),
                        
                        '空头占总股本比例（%）_yahoo': yahoo_info.get('shares_percent_shares_out', 0) * 100 if yahoo_info.get('shares_percent_shares_out') is not None else None,
                        '空头占流通股本比例（%）_yahoo': yahoo_info.get('short_percent_of_float', 0) * 100 if yahoo_info.get('short_percent_of_float') is not None else None,
                        '空头比率（空头持仓/日成交量）_yahoo': yahoo_info.get('short_ratio'),
                        '推荐评级_yahoo': yahoo_info.get('recommendation_key'),
                        '目标均价_yahoo': yahoo_info.get('target_mean_price'),
                        '目标中位价_yahoo': yahoo_info.get('target_median_price'),
                        '分析师数量_yahoo': yahoo_info.get('number_of_analyst_opinions'),
                        
                        # 分类信息
                        '所属指数_ib': ib_fund.get('index_constituents'),
                        'industry': contract_details.get('industry'),
                        'category': contract_details.get('category'),
                        'subcategory': contract_details.get('subcategory'),
                        'min_tick': min_tick,
                        
                        # 交易所相关
                        '交易所_ib': exchange_ib,
                        '交易所_wind': exchange_wind,
                        '主交易所_ib': contract_details.get('primary_exchange'),
                        '可选交易所_ib': contract_details.get('valid_exchanges'),
                        
                        # 上市时间相关字段
                        '上市时间': listing_time,  # 使用排序用的上市时间
                        '上市时间_ib': ib_head_time,
                        '上市时间_yahoo': yahoo_time,
                        '上市时间_wind': wind_time,
                        '上市时间_futu': futu_time,
                        '起始时间_db': db_time,
                        '结束时间_db': db_end_time, # 填充结束时间_db
                        
                        # 其他
                        '证券状态(上市L退市D新N)_wind': wind_stock.get('sec_status'),
                        '停牌天数_wind': wind_stock.get('susp_days'),
                        '记录存在_futu': 1 if ib_symbol in self.futu_products else None,
                        '收录复权_futu': 1 if futu_product.get('rehab_known') else None,
                        '日均成交额（万美元）_1M': None,  # 新增的日均成交额（万美元）_1M列
                        '收盘价_1M': None,  # 新增的收盘价_1M列
                        '月均中间价_ib': mid_price_ib,
                        '绝对盘口_ib': absolute_spread_ib,
                        '相对盘口（1/2000）_ib': relative_spread_1_2000_ib,
                        '相对盘口（万一）_ib': relative_spread_wan_yi_ib,
                        '盘口跳数_ib': ticks_ib,
                        '月均中间价_yahoo': mid_price_yahoo,
                        '绝对盘口_yahoo': absolute_spread_yahoo,
                        '相对盘口（1/2000）_yahoo': relative_spread_1_2000_yahoo,
                        '相对盘口（万一）_yahoo': relative_spread_wan_yi_yahoo,
                        '盘口跳数_yahoo': ticks_yahoo,
                    }

                    if self.ftp_data_available:
                        usa_row = self.usa_data[self.usa_data['conid'] == conid].iloc[0] if not self.usa_data[self.usa_data['conid'] == conid].empty else None
                        if usa_row is not None:
                            data_dict.update({
                                '现金利率-融券费率（%）': usa_row['现金利率-融券费率（%）'],
                                '融券费率（%）': usa_row['融券费率（%）'],
                                '融券池': usa_row['融券池']
                            })

                    # 如果是现有记录，保留原有ID
                    if not is_new and existing_id is not None:
                        data_dict['ID'] = existing_id
                    
                    # 将结果存入字典
                    all_data[(conid, exchange)] = data_dict
                    
                except Exception as e:
                    logger.error(f"处理 {conid} 时发生错误: {str(e)}")
                    logger.error(traceback.format_exc())

        # 创建所有任务
        tasks = []
        
        # 为现有记录创建任务
        for idx, row in df_existing.iterrows():
            conid = row['conid']
            exchange = row['交易所_ib']
            if not pd.isna(conid):
                tasks.append(process_symbol(conid, exchange, is_new=False, existing_id=row['ID']))
        
        # 为新记录创建任务
        for conid, exchange in new_symbols:
            tasks.append(process_symbol(conid, exchange, is_new=True))
        
        # 使用asyncio.gather并发执行所有任务
        logger.info(f"开始并发处理 {len(tasks)} 个任务...")
        await asyncio.gather(*tasks)
        logger.info("所有任务处理完成")

        # 处理新数据的排序和ID分配
        new_data = []
        for (conid, exchange), data in all_data.items():
            if (conid, exchange) in new_symbols:
                new_data.append(data)
        
        if new_data:
            df_new = pd.DataFrame(new_data)
            
            # 按上市时间（排序用）和symbol排序
            df_new_sorted = df_new.sort_values(['上市时间', 'symbol'], 
                                             ascending=[True, True])
            
            # 为新数据分配ID
            df_new_sorted['ID'] = range(self.max_id + 1, self.max_id + 1 + len(df_new_sorted))
            
            # 更新all_data中的ID
            for _, row in df_new_sorted.iterrows():
                all_data[(row['conid'], row['交易所_ib'])]['ID'] = row['ID']

        # 构建最终的DataFrame
        df_updated = pd.DataFrame([data for data in all_data.values()])
        
        # 确保新增的列在稳定ID表中
        for column in self.COLUMNS:
            if column not in df_updated.columns:
                df_updated[column] = None

        # 按ID排序
        df_updated = df_updated.sort_values('ID')[self.COLUMNS]

        # 增加TRADING状态列
        latest_trading_symbols = set(self.get_latest_trading_symbols())
        def get_trading_status(row):
            symbol_key = (str(row['conid']), row['交易所_ib'])
            if symbol_key in latest_trading_symbols:
                return 1
            elif pd.notnull(row['起始时间_db']):  # 有历史数据
                return 0
            return None

        df_updated['TRADING'] = df_updated.apply(get_trading_status, axis=1)
        
        # 设置同组状态列
        # set1 = 491653063,582332987,647324601,663134311,793654246,94078796,9869443
        def get_no_same_group_status(row):
            if pd.isna(row['assoc_entity_id']):
            # if pd.isna(row['assoc_entity_id']) or int(row['conid']) in set1:
                return 1  # 无相同assoc_entity_id
            
            # 查找相同assoc_entity_id的其他记录
            same_entity_records = df_updated[
                (df_updated['assoc_entity_id'] == row['assoc_entity_id']) & 
                (df_updated['conid'] != row['conid'])  # 排除自己
            ]

            return None if not same_entity_records.empty else 1  # 有相同的返回None，无相同的返回1
            # return None if not same_entity_records.empty or int(row['conid'])==741203871 else 1  # 有相同的返回None，无相同的返回1
        
        df_updated['无相同实体'] = df_updated.apply(get_no_same_group_status, axis=1)
        
        # 设置券池>100状态列
        def get_pool_status(row):
            pool_value = row['融券池']
            if pd.isna(pool_value):
                return None
            try:
                # 移除两端的'>'符号再转为整数
                pool_str = str(pool_value).strip()
                if pool_str.startswith('>'):
                    pool_str = pool_str[1:]
                pool_int = int(pool_str)
                return 1 if pool_int >= 100 else 0
            except (ValueError, TypeError):
                return None
        
        df_updated['券池>100'] = df_updated.apply(get_pool_status, axis=1)
        
        logger.info("ID数据准备完成。")
        return df_updated

    def calculate_indicators(self, df: pd.DataFrame, period: str) -> Dict[str, Any]:
        if df.empty or len(df) < 2:
            return {}

        closes = df['close_price']
        highs = df['high_price']
        lows = df['low_price']
        volumes = df['volume']
        turnovers = df['turnover']

        indicators: Dict[str, Any] = {
            f'最高价_{period}': highs.max(),
            f'最低价_{period}': lows.min(),
            f'均价_{period}': closes.mean(),
            f'收盘价_{period}': closes.iloc[-1],  # 添加收盘价
            f'ATR_{period}': talib.ATR(highs.values, lows.values, closes.values, timeperiod=len(df)-1)[-1],
            f'日均成交量（万）_{period}': volumes.mean() / 10000,  # 转换为万
            f'日均成交额（万美元）_{period}': turnovers.mean() / 10000,  # 转换为万美元
        }

        return indicators

    def prepare_trading_data(self, df_id: pd.DataFrame) -> pd.DataFrame:
        logger.info("正在准备交易数据...")
        results: List[Dict[str, Any]] = []
        
        for row in tqdm(df_id.itertuples(index=False), total=len(df_id), desc="处理交易数据"):
            conid = row.conid
            exchange = row.交易所_ib
            
            contract_data = {
                'ID': row.ID,
                'conid': conid,
                'conid_db': row.conid_db,
                'symbol': row.symbol,
                'TRADING': row.TRADING,
                '剔除': row.剔除,
                '交易所_ib': exchange,
                '中文名_futu': row.中文名_futu,
                '中文名_wind': row.中文名_wind,
                '名称_ib': row.名称_ib
            }
            
            # 使用conid_db加载数据
            conid_db = row.conid_db
            bars_4y = self.load_bar_data(conid_db, exchange, self.end_date - relativedelta(years=4), self.end_date)
            
            if not bars_4y:
                # 如果没有历史数据，将所有交易相关指标设置为None
                for period in PERIODS.keys():
                    for indicator in ['最高价', '最低价', '均价', '收盘价', 'ATR', '日均成交量（万）', '日均成交额（万美元）']:
                        contract_data[f'{indicator}_{period}'] = None
                results.append(contract_data)
                continue
            
            df_bars = pd.DataFrame([{
                'datetime': bar.datetime,
                'open_price': bar.open_price,
                'high_price': bar.high_price,
                'low_price': bar.low_price,
                'close_price': bar.close_price,
                'volume': bar.volume,
                'turnover': bar.volume * bar.close_price
            } for bar in bars_4y])
            
            for period, delta in PERIODS.items():
                period_start = self.end_date - delta
                
                try:
                    df_period = df_bars[df_bars['datetime'].dt.tz_convert(DB_TZ) >= period_start]
                except Exception as e:
                    logger.error(f"Error: {e}")
                    logger.error(f"Period Start: {period_start}")
                    logger.error(f"Data: {df_bars}")
                
                if not df_period.empty and df_bars['datetime'].min().tz_convert(DB_TZ).date() <= period_start.date():
                    indicators = self.calculate_indicators(df_period, period)
                    contract_data.update(indicators)
                else:
                    for indicator in ['最高价', '最低价', '均价', '收盘价', 'ATR', '日均成交量（万）', '日均成交额（万美元）']:
                        contract_data[f'{indicator}_{period}'] = None
            
            results.append(contract_data)

        logger.info("交易数据准备完成。")
        return pd.DataFrame(results)

    def calculate_correlation_matrix(self, df_trading: pd.DataFrame, period: str) -> pd.DataFrame:
        logger.info(f"正在计算 {period} 的相关性矩阵...")
        
        period_start = self.end_date - PERIODS[period]
        
        # 创建一个字典来存储每个股票的收盘价序列
        price_data = {}
        
        for row in tqdm(df_trading.itertuples(index=False), total=len(df_trading), desc="处理相关性矩阵"):
            conid = row.conid
            symbol = row.symbol
            exchange = row.交易所_ib
            
            # 使用conid_db加载数据
            conid_db = row.conid_db
            
            # 先加载4年的数据以命中缓存
            bars_4y = self.load_bar_data(conid_db, exchange, self.end_date - relativedelta(years=4), self.end_date)
            
            if bars_4y:
                # 从4年数据中筛选出当前周期的数据
                period_bars = [bar for bar in bars_4y if bar.datetime.replace(tzinfo=DB_TZ) >= period_start]
                
                if period_bars:
                    closes = pd.Series({bar.datetime: bar.close_price for bar in period_bars})
                    price_data[symbol] = closes
        
        if not price_data:
            logger.warning(f"警告：{period} 周期没有足够的数据来计算相关性矩阵。")
            return pd.DataFrame()
        
        # 将所有股票的收盘价数据合并到一个DataFrame中
        df_prices = pd.DataFrame(price_data)
        
        # 对齐日期索引，并向前填充缺失值
        df_prices = df_prices.fillna(method='ffill')
        
        # 删除所有值为NaN的列
        df_prices = df_prices.dropna(axis=1, how='all')
        
        # 计算相关性矩阵
        correlation_matrix = df_prices.corr()
        
        logger.info(f"{period} 的相关性矩阵计算完成。")
        return correlation_matrix

    def generate_excel(self, df_id: pd.DataFrame, df_trading: pd.DataFrame) -> None:
        """生成Excel文件"""
        logger.info("正在生成Excel文件...")
        
        # 如果启用use_hisfix或处理指定conid，使用hisfix文件夹
        if self.use_hisfix or self.target_conids:
            his_folder = 'hisfix'
        else:
            his_folder = 'his'
        
        # 处理时间列
        for col in ['上市时间_futu', '起始时间_db', '结束时间_db']:
            if col in df_id.columns and not df_id[col].isna().all():  # 列存在且不全是NA
                # 检查是否所有非NA值的时分秒都是0
                all_zeros = all(
                    (t.hour == 0 and t.minute == 0 and t.second == 0) 
                    for t in df_id[col].dropna() 
                    if isinstance(t, datetime)
                )
                if all_zeros:
                    df_id[col] = df_id[col].apply(lambda x: x.date() if isinstance(x, datetime) else x)
        
        # 定义列配置：小数位数和列名列表
        column_configs = {
            5: [  # 价格相关列
                '最高价_1M', '最低价_1M', '均价_1M', '收盘价_1M', 'ATR_1M',
                '最高价_3M', '最低价_3M', '均价_3M', 'ATR_3M',
                '最高价_6M', '最低价_6M', '均价_6M', 'ATR_6M',
                '最高价_1Y', '最低价_1Y', '均价_1Y', 'ATR_1Y',
                '最高价_2Y', '最低价_2Y', '均价_2Y', 'ATR_2Y',
                '最高价_3Y', '最低价_3Y', '均价_3Y', 'ATR_3Y',
                '目标均价_yahoo', '目标中位价_yahoo', 
                '月均中间价_ib', '绝对盘口_ib', '月均中间价_yahoo', '绝对盘口_yahoo'
            ],
            4: [  # 亿相关列
                '总股本（亿）', '总股本（亿）_ib', '总股本（亿）_yahoo', '总股本（亿）_wind',
                '流通股本（亿）', '流通股本（亿）_ib', '流通股本（亿）_yahoo',
                '市值（亿美元）', '市值（亿美元）_ib', '市值（亿美元）_yahoo',
                '总收入（亿美元）', '总收入（亿美元）_ib', '总收入（亿美元）_yahoo',
                '毛利（亿美元）_yahoo', '总现金（亿美元）_yahoo', '总债务（亿美元）_yahoo',
                '月均现金利率-融券费率（%）', '月均融券费率（%）' , '现金利率-融券费率（%）', '融券费率（%）'
            ],
            3: [  # 比率和成交量相关列
                '市盈率', '市盈率_ib', '市盈率_yahoo', '市盈率_wind',
                '市净率', '市净率_ib', '市净率_yahoo', '市净率_wind',
                '换手率（%）_wind',
                '空头占总股本比例（%）_yahoo', '空头占流通股本比例（%）_yahoo',
                '日均成交量（万）_1M', '日均成交量（万）_3M', '日均成交量（万）_6M',
                '日均成交量（万）_1Y', '日均成交量（万）_2Y', '日均成交量（万）_3Y',
                '日均成交额（万美元）_1M', '日均成交额（万美元）_3M', '日均成交额（万美元）_6M',
                '日均成交额（万美元）_1Y', '日均成交额（万美元）_2Y', '日均成交额（万美元）_3Y'
            ],
            2: [  # 百分比相关列
                '股息收益率（%）', '股息收益率（%）_yahoo', '股息收益率（%）_wind',
                '空头比率（空头持仓/日成交量）_yahoo',
                '流通股本占总股本比例（%）',
                '盘口跳数_ib', '相对盘口（1/2000）_ib', '相对盘口（万一）_ib', 
                '盘口跳数_yahoo', '相对盘口（1/2000）_yahoo', '相对盘口（万一）_yahoo'
            ]
        }
        
        # 构建需要右对齐的列列表
        right_align_cols = []
        for cols in column_configs.values():
            right_align_cols.extend(cols)
        right_align_cols.extend(['分析师数量_yahoo'])
        
        # 统一处理小数位数格式化
        for decimal_places, columns in column_configs.items():
            for col in columns:
                for df in [df_id, df_trading]:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: f"{x:.{decimal_places}f}" if pd.notnull(x) else x)
        
        # 只对主表中之前使用科学计数法的列进行格式化
        # scientific_columns = ['相对盘口（1/2000）', '相对盘口（万一）']
        # for col in scientific_columns:
        #     if col in df_id.columns:
        #         df_id[col] = df_id[col].apply(format_number)

        with pd.ExcelWriter(EXCEL_FILE, engine='openpyxl') as writer:
            # 确保新增的列在稳定ID表中，并将稳定ID的列名放到最前面
            df_id = df_id[self.COLUMNS]
            df_id.to_excel(writer, sheet_name='稳定ID', index=False)
            
            if self.save_option in ['periods', 'all']:
                for period in tqdm(PERIOD_NAMES, desc="生成周期数据"):
                    logger.info(f"正在处理 {period} 周期...")
                    # 选择基础列和当前周期的列
                    base_cols = ['ID', 'conid', 'conid_db', 'symbol', 'TRADING', '剔除', '中文名_futu', '中文名_wind', '名称_ib']
                    period_cols = [col for col in df_trading.columns if col.endswith(period)]
                    
                    # 如果是1M周期，保留收盘价列，否则移除
                    if period != '1M':
                        period_cols = [col for col in period_cols if not col.startswith('收盘价_')]
                    
                    df_period = df_trading[base_cols + period_cols].copy()
                    
                    if df_period.empty:
                        logger.warning(f"警告：{period} 周期没有足够的数据，跳过此周期。")
                        continue
                    
                    df_period.to_excel(writer, sheet_name=period, index=False)

            if self.save_option == 'all':
                for period in tqdm(PERIOD_NAMES, desc="生成相关性矩阵"):
                    logger.info(f"正在处理 {period} 的相关性矩阵...")
                    correlation_matrix: pd.DataFrame = self.calculate_correlation_matrix(df_trading, period)
                    if correlation_matrix.empty:
                        logger.warning(f"警告：{period} 周期没有足够的数据来计算相关性矩阵，跳过此周期。")
                        continue
                    correlation_matrix.to_excel(writer, sheet_name=f'cor_{period}')

        # 使用 auto_adjust_worksheet 函数调整所有工作表
        workbook = load_workbook(EXCEL_FILE)
        for sheet_name in tqdm(workbook.sheetnames, desc="调整工作表"):
            worksheet = workbook[sheet_name]
            # 创建自定义对齐方式配置
            column_alignments = {}
            for col_idx, column in enumerate(worksheet.columns, 1):
                header_value = column[0].value
                if header_value in right_align_cols:
                    column_alignments[get_column_letter(col_idx)] = 'right'
            
            auto_adjust_worksheet(worksheet, column_alignments=column_alignments)
        workbook.save(EXCEL_FILE)
        logger.info("Excel文件生成完成。")

        # 另存为一份重命名的文件
        os.makedirs(his_folder, exist_ok=True)
        new_file_name = f'{his_folder}/{os.path.splitext(os.path.basename(EXCEL_FILE))[0]}{self.end_date.strftime("%Y%m%d")}.xlsx'
        shutil.copy(EXCEL_FILE, new_file_name)
        logger.info(f"文件已保存为: {new_file_name}")

    async def run(self) -> None:
        logger.info("开始数据汇总过程...")
        await self.connect_ib()
        symbols = await self.get_all_symbols()
        df_id: pd.DataFrame = await self.update_stable_ids(symbols)
        logger.debug(df_id.head())
        
        if self.save_option in ['periods', 'all']:
            df_trading: pd.DataFrame = self.prepare_trading_data(df_id)
            logger.debug(df_trading.head())
            # 将收盘价_1M和日均成交额（万美元）_1M合并到df_id
            if '日均成交额（万美元）_1M' in df_trading.columns and not df_trading.empty:
                df_id['日均成交额（万美元）_1M'] = df_id['ID'].map(df_trading.set_index('ID')['日均成交额（万美元）_1M'])
            else:
                df_id['日均成交额（万美元）_1M'] = None # 如果 df_trading 为空或没有 '日均成交额（万美元）_1M' 列
                logger.warning("df_trading 为空或没有 '日均成交额（万美元）_1M' 列")

            if '收盘价_1M' in df_trading.columns and not df_trading.empty:
                df_id['收盘价_1M'] = df_id['ID'].map(df_trading.set_index('ID')['收盘价_1M'])
            else:
                df_id['收盘价_1M'] = None # 如果 df_trading 为空或没有 '收盘价_1M' 列
                logger.warning("df_trading 为空或没有 '收盘价_1M' 列")
        else:
            df_trading = pd.DataFrame()  # 空的DataFrame，因为我们只需要稳定ID
            df_id['收盘价_1M'] = None # 在id_only模式下，收盘价_1M填充为NaN
            df_id['日均成交额（万美元）_1M'] = None # 在id_only模式下，日均成交额（万美元）_1M填充为NaN

        # 保存新的合约详情到数据库
        if self.new_contract_details:
            logger.info(f"保存 {len(self.new_contract_details)} 个新的合约详情到数据库...")

            # 将ContractDetails对象转换为可保存的字典
            details_to_save = [contract_details_to_dict(conid, details) for conid, details in self.new_contract_details.items()]
            
            # 批量保存到数据库
            db_manager.batch_save_to_db(
                data_list=details_to_save,
                model_class=IbContractDetail,
                primary_key="conid"
            )
            logger.info("合约详情保存完成")
            self.new_contract_details = {}

        # 设置成交额状态列 - 需要在日均成交额数据合并后处理
        def get_turnover_status(row):
            turnover_1m = row['日均成交额（万美元）_1M']
            if pd.isna(turnover_1m):
                return None
            try:
                turnover_value = float(turnover_1m) if isinstance(turnover_1m, str) else turnover_1m
                return 1 if turnover_value >= 1500 else 0
            except (ValueError, TypeError):
                return None

        df_id['成交额>1500'] = df_id.apply(get_turnover_status, axis=1)

        # 处理剔除不交易字段 - 使用新增的辅助列
        # 先计算每个assoc_entity_id组中成交额最大的记录
        max_turnover_ids = set()

        # 按assoc_entity_id分组，找出每组中成交额最大的记录
        for entity_id, group in df_id.groupby('assoc_entity_id'):
            if pd.notna(entity_id):  # 只处理有assoc_entity_id的组
                # 找到成交额最大的记录，使用简单的排序方式
                max_record = None
                max_turnover = None
                max_has_value = False
                
                for idx, row in group.iterrows():
                    turnover = row['日均成交额（万美元）_1M']
                    has_value = pd.notna(turnover)
                    
                    # 判断是否应该更新最大记录
                    should_update = False
                    if max_record is None:
                        should_update = True
                    elif has_value and not max_has_value:
                        # 当前有值，之前最大的没值
                        should_update = True
                    elif has_value and max_has_value and turnover > max_turnover:
                        # 都有值，当前更大
                        should_update = True
                    
                    if should_update:
                        max_record = row
                        max_turnover = turnover
                        max_has_value = has_value
                
                if max_record is not None:
                    max_turnover_ids.add(max_record['ID'])

        def _load_conids_from_file(filename: str) -> Set[int]:
            try:
                with open(filename, 'r') as file:
                    return {int(line.strip()) for line in file if line.strip().isdigit()}
            except FileNotFoundError:
                logger.warning(f"文件 {filename} 未找到，返回空集合。")
                return set()

        latest_ids = _load_conids_from_file('latest_ids.txt')
        vol_ids = _load_conids_from_file('vol_ids.txt')

        def get_exclude_status(row):
            # 利用新增的辅助列进行判断
            whitelist_status = row['类型白名单']  # 在白名单中, None: 不在白名单中或白名单为空
            trading_status = row['TRADING']  # 最新交易, 0: 有历史数据, None: 无数据
            turnover_status = row['成交额>1500']  # >=1500, 0: <1500, None: 无数据
            pool_status = row['券池>100']  # >=100, 0: <100, None: 无数据
            no_same_group_status = row['无相同实体']  # 无相同assoc_entity_id, None: 有相同assoc_entity_id

            # 2.5. 检查symbol是否以.OLD或.OLD1或 WI结尾，或者交易所为VALUE或主交易所为PINK
            symbol = row['symbol']
            
            if (symbol and (symbol.endswith('.OLD') or symbol.endswith('.OLD1') or symbol.endswith(' WI'))) or \
               row['交易所_ib'] == 'VALUE' or row['主交易所_ib'] == 'PINK':
                return 5

            # 2.4. no_same_group_status为None，且turnover_status为0，且不是相同assoc_entity_id中成交额最大的
            # 有相同底层实体+不是同组中成交额最大的记录+成交额小于1500=直接删
            if pd.isna(no_same_group_status) and turnover_status != 1 and row['ID'] not in max_turnover_ids:
                return 4

            # 按顺序依次赋值，默认为0
            # 2.3. whitelist_status为None且（trading_status为None或no_same_group_status为None）
            # 不在股票类型白名单+无日线=直接删
            # 不在股票类型白名单+有相同底层实体=直接删
            if pd.isna(whitelist_status) and ((pd.isna(trading_status)
                                               # and int(row['conid']) not in latest_ids
                                              ) or pd.isna(no_same_group_status)):
                return 3

            if int(row['conid']) not in latest_ids:
                return 2

            result = 0
            if int(row['conid']) not in vol_ids:
                result -= 10

            # 2.1. whitelist_status为None
            if pd.isna(whitelist_status):
                return result-1

            # 2.2. TRADING为None
            if pd.isna(trading_status):
                return result-2

            # 2.5. '券池>100'不为1
            if pool_status != 1:
                return result-5

            # 默认为空
            return None

        df_id['剔除'] = df_id.apply(get_exclude_status, axis=1)
        
        # 将计算好的TRADING和剔除列值合并到df_trading的占位列中
        if not df_trading.empty and '剔除' in df_trading.columns:
            exclude_mapping = df_id.set_index('ID')['剔除']
            
            # 将值合并到df_trading中
            df_trading['剔除'] = df_trading['ID'].map(exclude_mapping)

        # 删除新赋予的ID中"剔除"列不为0的记录并重新分配连续ID
        logger.info("正在删除新赋予的ID中剔除列>0的记录并重新分配ID...")
        
        # 记录删除前的总数
        total_before = len(df_id)
        
        # 直接过滤掉ID > max_id且"剔除"列>0的行
        df_id = df_id[~((df_id['ID'] > self.max_id) & (df_id['剔除'] > 0))]
        
        # 记录删除后的总数
        total_after = len(df_id)
        deleted_count = total_before - total_after
        
        # 对>max_id的ID做映射修改，使其连续
        new_records = df_id[df_id['ID'] > self.max_id].sort_values('ID')
        if not new_records.empty:
            # 创建旧ID到新ID的映射
            old_ids = new_records['ID'].tolist()
            new_ids = list(range(self.max_id + 1, self.max_id + 1 + len(old_ids)))
            id_mapping = dict(zip(old_ids, new_ids))
            
            # 应用映射
            df_id.loc[df_id['ID'] > self.max_id, 'ID'] = df_id.loc[df_id['ID'] > self.max_id, 'ID'].map(id_mapping)
            
            logger.info(f"新记录ID重新分配完成，新记录ID范围: {self.max_id + 1} 到 {self.max_id + len(old_ids)}")
        
        logger.info(f"删除了 {deleted_count} 条新赋予ID中剔除列不为0的记录，剩余 {total_after} 条记录")
        
        # 同样处理df_trading数据
        if not df_trading.empty:
            # 过滤掉ID > max_id且"剔除"列不为0的行
            df_trading = df_trading[~((df_trading['ID'] > self.max_id) & (df_trading['剔除'] > 0))]
            
            # 应用相同的ID映射
            if not new_records.empty:
                df_trading.loc[df_trading['ID'] > self.max_id, 'ID'] = df_trading.loc[df_trading['ID'] > self.max_id, 'ID'].map(id_mapping)
            
            logger.info(f"df_trading也已处理，剩余 {len(df_trading)} 条记录")

        self.generate_excel(df_id, df_trading)
        await self.disconnect_ib()
        logger.info("数据汇总过程完成。")

class SaveOption(str, Enum):
    """保存选项枚举"""
    ID_ONLY = "id_only"
    PERIODS = "periods"
    ALL = "all"

app = typer.Typer()

@app.command()
def run(
    end_date: str = typer.Option(None, "--end_date", "-e", help="End date for data processing (YYYY-MM-DD)"),
    save_option: SaveOption = typer.Option(SaveOption.PERIODS, "--save_option", "-s", 
                                         help="Save option: 'id_only' for stable ID, 'periods' for ID and period data, 'all' for everything"),
    offset: int = typer.Option(0, "--offset", "-o", help="Client ID offset for IB connection"),
    conids: str = typer.Option(None, "--conids", "-c", help="Comma-separated list of conids to process (e.g., 265598,10098)"),
    use_hisfix: bool = typer.Option(False, "--use_hisfix", "-f", help="Save files to hisfix folder instead of his folder")
) -> None:
    """运行股票数据汇总程序"""
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d") if end_date else None
    
    # 解析conids参数
    target_conids = None
    if conids:
        target_conids = [int(conid.strip()) for conid in conids.split(',') if conid.strip()]
    
    asyncio.run(main(end_date_obj, save_option.value, offset, target_conids, use_hisfix))

async def main(end_date: Optional[datetime] = None, save_option: str = 'all', offset: int = 0, target_conids: Optional[List[int]] = None, use_hisfix: bool = False) -> None:
    """主函数"""
    import time
    start_time: float = time.time()
    
    summary: StockDataSummary = StockDataSummary(end_date, save_option, offset, target_conids, use_hisfix)
    await summary.run()
    
    end_time: float = time.time()
    total_time: float = end_time - start_time
    logger.info(f"总执行时间：{total_time:.2f} 秒")

if __name__ == "__main__":
    app()

