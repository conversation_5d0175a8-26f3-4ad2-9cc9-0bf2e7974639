#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
企业微信机器人告警推送工具

使用方法:
from wecom_alert import report_we_alert, WecomAlertManager

# 单条消息发送
success, msg = report_we_alert(
    text="测试消息",
    key="your-wecom-key-here"
)

# 使用消息管理器
manager = WecomAlertManager()
manager.start()  # 启动消息处理线程
manager.add_message("测试消息")  # 添加消息到队列
manager.stop()  # 停止消息处理线程
"""

import traceback
import requests
import queue
import threading
import time
from typing import Optional, List
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


import urllib.request, os

def get_system_proxy() -> dict:
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}

    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            os.environ[f"{protocol}_proxy"] = proxy

    return proxies

proxies = get_system_proxy()

def report_we_alert(text: str, key: str="ee0b6801-f2c5-4811-ba1f-227b543b3459") -> tuple[bool, str]:
    """
    发送消息到企业微信机器人，发送的消息不能超过20条/分钟。
    
    参数:
        text (str): 要发送的文本消息
        key (str): 企业微信机器人的webhook key
        
    返回:
        tuple[bool, str]: (是否成功, 成功/错误消息)
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"
    payload = {
        "msgtype": "text",
        "text": {
            "content": text # 文本内容，最长不超过2048个字节，必须是utf8编码
            }
    }
    
    try:
        response = requests.post(url, json=payload, proxies=proxies)
        
        if response.status_code == 200:
            response_json = response.json()
            
            if response_json.get("errcode") == 0:
                return True, response_json.get("errmsg", "操作成功")
            else:
                return False, response_json.get("errmsg", "未知错误")
        else:
            return False, f"HTTP错误: {response.status_code}，{response.text}"
            
    except requests.RequestException as e:
        return False, f"请求异常: {str(e)}"
    except Exception as e:
        return False, f"未知错误: {str(e)}\n{traceback.format_exc()}"

class WecomAlertManager:
    """企业微信消息管理器，处理消息队列和限流"""
    
    def __init__(self, key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459", interval: int = 10):
        """初始化消息管理器
        
        Args:
            key: 企业微信机器人的webhook key
            interval: 消息处理间隔（秒），默认10秒
        """
        self.message_queue = queue.Queue()
        self.is_stopping = False
        self.message_thread = None
        self.key = key
        self.interval = interval  # 消息处理间隔
        
    def start(self):
        """启动消息处理线程"""
        if self.message_thread is None or not self.message_thread.is_alive():
            self.is_stopping = False
            self.message_thread = threading.Thread(target=self._process_messages)
            self.message_thread.daemon = True
            self.message_thread.start()
            
    def stop(self):
        """停止消息处理线程，等待所有消息处理完成"""
        self.is_stopping = True
        if self.message_thread:
            self.message_thread.join()
            
    def add_message(self, message: str):
        """添加消息到队列"""
        self.message_queue.put(message)

    def _collect_messages(self) -> List[str]:
        """从队列中收集消息，直到超过大小限制"""
        messages = []
        total_size = 0
        
        # 尝试从队列中获取尽可能多的消息
        while True:
            try:
                message = self.message_queue.get_nowait()
                message_size = len(message.encode('utf-8'))
                
                # 如果单条消息就超过2048字节，截断处理
                if message_size > 2048:
                    # 找到合适的截断位置，确保不超过2048字节
                    truncated = message[:2000]  # 留一些余量给后缀
                    remaining = message[2000:]
                    
                    truncated_with_suffix = truncated + "...[续]"
                    message = truncated_with_suffix
                    message_size = len(message.encode('utf-8'))
                    
                    # 将剩余部分重新放入队列
                    if remaining.strip():
                        self.message_queue.put(remaining)
                
                # 如果加上这条消息会超过限制，把消息放回队列并退出
                if total_size > 0 and total_size + message_size + 1 > 2048:  # +1 是为了考虑换行符
                    self.message_queue.put(message)
                    break
                    
                # 否则添加这条消息
                messages.append(message)
                total_size += message_size + 1  # +1 是为了考虑换行符
                
            except queue.Empty:
                break
                
        return messages
        
    def _process_messages(self):
        """处理消息队列"""
        pending_messages = []  # 用于存储待重试的消息
        
        while True:
            time.sleep(self.interval)
            
            # 先处理pending_messages中的消息
            if pending_messages:
                content = "\n".join(pending_messages)
                success, error_msg = report_we_alert(content, self.key)
                
                if success:
                    pending_messages.clear()
                continue
            
            # 从队列收集新消息
            messages = self._collect_messages()
            
            # 如果有消息要发送
            if messages:
                content = "\n".join(messages)
                success, error_msg = report_we_alert(content, self.key)
                
                if not success:
                    # 发送失败，将消息加入待重试列表
                    pending_messages.extend(messages)
            
            # 如果正在停止，检查是否还有待处理的消息
            if self.is_stopping:
                if self.message_queue.empty() and not pending_messages:
                    logger.info("消息处理线程正常退出，所有消息已处理完成")
                    break
                else:
                    # 仍有消息未发出，记录日志但继续处理
                    queue_size = self.message_queue.qsize()
                    pending_size = len(pending_messages)
                    logger.warning(f"收到停止信号但仍有消息未发出 - 队列中消息数: {queue_size}, 待重试消息数: {pending_size}")
                    # 不break，继续处理剩余消息

if __name__ == "__main__":
    # 测试示例
    success, msg = report_we_alert("这是一条测试消息")
    print(f"发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")
